spring.application.name=integration-hub
server.port=8018

flexcore.portal.home.dir=D:/P3/integration-hub
flexcore.portal.conf.dir=${flexcore.portal.home.dir}/conf
flexcore.portal.web.static-resource.dir=${flexcore.portal.home.dir}/upload
flexcore.portal.web.static-resource-save.dir=${flexcore.portal.home.dir}/save
flexcore.portal.web.static-resource-sdk.dir=${flexcore.portal.home.dir}/sdk
flexcore.portal.params.file=params.json

#spring.data.mongodb.host=**************
#spring.data.mongodb.port=27194
#spring.data.mongodb.database=flex_inspection
#spring.data.mongodb.username=admin
#spring.data.mongodb.password=admin
#spring.data.mongodb.auto-index-creation=true
#spring.data.mongodb.fail-fast=false




spring.data.mongodb.host: ***********
spring.data.mongodb.port: 27017
spring.data.mongodb.database: flexdata
spring.data.mongodb.username: admin
spring.data.mongodb.password: b6pqtIJu8O8t7LHC2PKjtdWo
spring.data.mongodb.authentication-database: admin
spring.data.mongodb.auto-index-creation: true

spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

#integration.hub.lgsp.hostLgsp=http://***********:8080
integration.hub.lgsp.hostLgsp=http://qa-gw.bcy.gov.vn/s/api/vpcp/vxp/1.0/adapter
integration.hub.lgsp.secretVDXP=A1niuGwtNHVHnbIcQWLkxwNw4G/6YFIB+b4ziRegYLn8
integration.hub.lgsp.tokenUrl=/security/oauth/token
integration.hub.lgsp.getReceivedEdocListUrl=/XrdAdapter/adapter/getReceivedEdocList
integration.hub.lgsp.sendEdocUrl=/XrdAdapter/adapter/sendEdoc
integration.hub.lgsp.getEdocUrl=/XrdAdapter/adapter/getEdoc
integration.hub.lgsp.updateStatusUrl=/XrdAdapter/adapter/updateStatus

integration.hub.lgsp.job.receiveEdoc.time=600000
integration.hub.lgsp.job.sendEdoc.time=2000
integration.hub.lgsp.job.receiveEdoc.enabled=false
integration.hub.lgsp.job.sendEdoc.enabled=false

integration.dancu.providerUrl=http://***********:7003
integration.dancu.dstCode=VN:GOV:G01:CSDLDC
integration.dancu.username=BKHCN_TTCNTT
#integration.dancu.username=BO_KHCN_TEST
integration.dancu.secretKey=KHCN9#aTkMr2aX
integration.dancu.host=http://************:8080 
integration.dancu.serviceTraCuuThongTinCongDan=/integration/createRequest/CommonService/ChiaSeThongTinCongDan

integration.hub.distributed.cache.redis.host=*************
integration.hub.distributed.cache.redis.port=6379
integration.hub.distributed.property.filePath=file:/integration-hub/bin/application-ext.properties







#==========Eureka====================
#eureka.client.service-url.defaultZone: http://**************:8015/eureka



