package com.example.integration.hub.service.impl;


import com.example.integration.config.PortalUtil;
import com.example.integration.config.PropKey;
import com.example.integration.constant.ApiConstant;
import com.example.integration.constant.Constant;
import com.example.integration.entity.HangDoiGoiTin;
import com.example.integration.entity.TepDuLieu;
import com.example.integration.entity.TrucTichHop;
import com.example.integration.entity.ext.HeThongKetNoiExt;
import com.example.integration.entity.ext.TrangThaiLienThongExt;
import com.example.integration.hub.action.SDKVXPAction;
import com.example.integration.hub.action.impl.SDKVXPActionImpl;
import com.example.integration.hub.service.HangDoiGoiTinService;
import com.example.integration.hub.service.TepDuLieuService;
import com.example.integration.hub.service.TrucTichHopService;
import com.fds.flex.common.ultility.GetterUtil;
import com.fds.flex.common.ultility.Validator;
import com.fds.flex.common.utility.string.StringPool;
import com.vpcp.services.model.*;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.validation.Valid;
import java.io.File;
import java.nio.file.Files;
import java.util.*;

@Setter
@Getter
@Service
@ConfigurationProperties(prefix = "integration.hub.lgsp")
@Slf4j
public class SchedulerService {
    @Autowired
    RestTemplate restTemplate;

    @Autowired
    TrucTichHopService trucTichHopService;

    @Autowired
    HangDoiGoiTinService hangDoiGoiTinService;

    @Autowired
    TepDuLieuService tepDuLieuService;

    private String hostLgsp;
    private String secretVDXP;
    private String tokenUrl;
    private String getReceivedEdocListUrl;
    private String sendEdocUrl;
    private String getEdocUrl;
    private String updateStatusUrl;
    private String systemId;
    private String version;
    private String serviceId;
    private String lgspTo;
    private String basicUsername;
    private String basicPassword;
    private String username;
    private String password;

    public int startSendEdoc() {
        secretVDXP = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SECRET_VDXP), StringPool.BLANK);
        systemId= GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SYSTEMID), StringPool.BLANK);
        version= GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_VERSION), StringPool.BLANK);
        serviceId= GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SERVICEID), StringPool.BLANK);
        lgspTo= GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_LGSPTO), StringPool.BLANK);
        basicUsername= GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_BASIC_USERNAME), StringPool.BLANK);
        basicPassword= GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_BASIC_PASSWORD), StringPool.BLANK);
        username= GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_USERNAME), StringPool.BLANK);
        password= GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_PASSWORD), StringPool.BLANK);

        SDKVXPAction sdkVxpAction = new SDKVXPActionImpl(hostLgsp, systemId, secretVDXP);

        log.info("Version: " + version + ", systemId:" + systemId + ", lgspTo: " + lgspTo);

        int countDocSendSuccess = 0;

        try {
//            List<TrucTichHop> trucTichHops = trucTichHopService.findAll();
//
//            if(Validator.isNull(trucTichHops) || trucTichHops.size() == 0) {
//                log.warn("No tructichhop was found");
//                return 0;
//            }
            Pageable pageable = PageRequest.of(0, 10);
            Page<HangDoiGoiTin> pageHangDoiGoiTin = hangDoiGoiTinService.filterForScheduler(Constant.SEND_EDOC, Constant.STATUS_INITIAL, Constant.HETHONG_SERVER, pageable);

            if(Validator.isNull(pageHangDoiGoiTin)) {
                log.warn("No Page<HangDoiGoiTin> was found");
                return 0;
            }

            List<HangDoiGoiTin> hangDoiGoiTins = pageHangDoiGoiTin.getContent();

            if(Validator.isNull(hangDoiGoiTins) || hangDoiGoiTins.size() == 0) {
                log.warn("No List<HangDoiGoiTin> was found");
                return 0;
            }

            for(HangDoiGoiTin hangDoiGoiTin : hangDoiGoiTins) {
                try {
                    TrangThaiLienThongExt trangThaiLienThongExt = new TrangThaiLienThongExt(Constant.STATUS_PROCESSING);

                    String statusSendOutside = Constant.STATUS_INITIAL;
                    for(HeThongKetNoiExt noiNhanGoiTin: hangDoiGoiTin.getNoiNhanGoiTin()) {
                        if(noiNhanGoiTin.getGiaoThucKetNoi().getMaMuc().equals(Constant.HETHONG_SERVER)) {
                            //chỉ gửi cho đơn vị ngoài
                            noiNhanGoiTin.setTrangThaiLienThong(trangThaiLienThongExt);
                            noiNhanGoiTin.setThoiGianKetNoi(System.currentTimeMillis());
                            hangDoiGoiTinService.update(hangDoiGoiTin);

                            //Chỉ gửi văn bản liên thông lên QG 1 lần
                            if(statusSendOutside.equals(Constant.STATUS_INITIAL)) {
                                boolean successSendEdoc = this.sendEdocUseSDK(sdkVxpAction, hangDoiGoiTin);

                                if(successSendEdoc) {
                                    trangThaiLienThongExt = new TrangThaiLienThongExt(Constant.STATUS_DONE);
                                    noiNhanGoiTin.setTrangThaiLienThong(trangThaiLienThongExt);
                                    hangDoiGoiTinService.update(hangDoiGoiTin);
                                    statusSendOutside = Constant.STATUS_DONE;
                                } else {
                                    trangThaiLienThongExt =  new TrangThaiLienThongExt(Constant.STATUS_FAIL);
                                    noiNhanGoiTin.setTrangThaiLienThong(trangThaiLienThongExt);
                                    hangDoiGoiTinService.update(hangDoiGoiTin);
                                    statusSendOutside = Constant.STATUS_FAIL;
                                }

                            } else if(statusSendOutside.equals(Constant.STATUS_FAIL)) {
                                trangThaiLienThongExt =  new TrangThaiLienThongExt(Constant.STATUS_FAIL);
                                noiNhanGoiTin.setTrangThaiLienThong(trangThaiLienThongExt);
                                hangDoiGoiTinService.update(hangDoiGoiTin);
                            } else {
                                trangThaiLienThongExt =  new TrangThaiLienThongExt(Constant.STATUS_DONE);
                                noiNhanGoiTin.setTrangThaiLienThong(trangThaiLienThongExt);
                                hangDoiGoiTinService.update(hangDoiGoiTin);
                            }
                        }
                    }

                    countDocSendSuccess++;
                    log.info("Goi with madinhdanh" + hangDoiGoiTin.getMaDinhDanh() + "has been send successful!");
                } catch (Exception e) {
                    log.warn("Hang doi goi tin: " + hangDoiGoiTin.getMaDinhDanh() +" send fail: ", e);
                }
            }
        } catch(Exception e) {
            log.error("Error when sendEdoc: ", e);
        }
        return countDocSendSuccess;
    }

    public int startReceiveEdoc() {
        secretVDXP = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SECRET_VDXP), StringPool.BLANK);
        systemId= GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SYSTEMID), StringPool.BLANK);
        version= GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_VERSION), StringPool.BLANK);
        serviceId= GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SERVICEID), StringPool.BLANK);
        lgspTo= GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_LGSPTO), StringPool.BLANK);
        basicUsername= GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_BASIC_USERNAME), StringPool.BLANK);
        basicPassword= GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_BASIC_PASSWORD), StringPool.BLANK);
        username= GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_USERNAME), StringPool.BLANK);
        password= GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_PASSWORD), StringPool.BLANK);
        SDKVXPAction sdkVxpAction = new SDKVXPActionImpl(hostLgsp, systemId, secretVDXP);

        int countDocReceiveSuccess = 0;

        try {
            List<TrucTichHop> trucTichHops = trucTichHopService.findAll();

            if(Validator.isNull(trucTichHops) || trucTichHops.size() == 0) {
                log.warn("No tructichhop was found");
                return 0;
            }

            //Lấy gói tin gửi đến các trục con từ quốc gia, logic: lấy các gói tin quốc gia gửi đến systemId trục cha
            //Sử dụng các trường from, to để xác định gửi đến đơn vị con nào, để xác định các trục con của đơn vị đó
            //Mỗi một docId sẽ tạo 1 hàng đợi gói tin map với trục con vừa xác định ở trên
            JSONArray listEdocReceived = this.getListEdocUseSDK(sdkVxpAction);

            for(int i = 0; i<listEdocReceived.length(); i++) {
                try {
                    JSONObject oneDoc = listEdocReceived.getJSONObject(i);
                    if(!oneDoc.has("docId") || Validator.isNull(oneDoc.getString("docId"))) {
                        log.warn("docId is null, skip");
                        continue;
                    }

                    if(!oneDoc.has("from") || Validator.isNull(oneDoc.getString("from"))) {
                        log.warn("from is null");
                        continue;
                    }

                    if(!oneDoc.has("to") || Validator.isNull(oneDoc.getString("to"))) {
                        log.warn("from is null");
                        continue;
                    }

                    String edocId = oneDoc.getString("docId");
                    String donViGui = oneDoc.getString("from");
                    String donViNhan = oneDoc.getString("to");

                    String edocResult = this.getEdocUseSDK(sdkVxpAction, edocId);

                    oneDoc.put("urlFile", edocResult);
                    if(Validator.isNull(edocResult)) {
                        continue;
                    }

                    HangDoiGoiTin hangDoiGoiTin = this.saveEdocVer2(oneDoc, trucTichHop);
                    if(Validator.isNull(hangDoiGoiTin)) {
                        continue;
                    }

                    log.info("Temp EDOCId: " + edocId + " saved");

                    boolean resultUpdateStatus = this.updateStatusUseSDK(sdkVxpAction, edocId, Constant.DONE);

                    if(resultUpdateStatus) {
                        log.info("Document EDOC " + edocId + " has been verify successful!");
                        countDocReceiveSuccess++;
                        continue;
                    }

                    log.warn("Document EDOC " + edocId + " has been verify fail");
                    hangDoiGoiTinService.delete(hangDoiGoiTin);
                    log.warn("Document EDOC " + edocId + " has been DELETE");
                } catch (Exception e) {
                    log.warn("Error when running one Edoc " + e.getMessage());
                }
            }




            for(TrucTichHop trucTichHop : trucTichHops) {
                List<HeThongKetNoiExt> heThongKetNois = trucTichHop.getHeThongKetNoi();

                if(Validator.isNull(heThongKetNois) || heThongKetNois.isEmpty()) {
                    log.warn("Not found heThongKetNois in truc: " + trucTichHop.getTenMuc());
                    continue;
                }


                for(HeThongKetNoiExt heThongNhanGoiTin: heThongKetNois) {
                    if(heThongNhanGoiTin.getGiaoThucKetNoi().getMaMuc().equals(Constant.HETHONG_SERVER)) {
                        continue;
                    }

                    log.info("Receiving edoc for client: " + heThongNhanGoiTin.getTenKetNoi());

                }
            }

            return countDocReceiveSuccess;
        }  catch (Exception e) {
            log.error("Error when receiveEdoc: ", e);
        }
        return countDocReceiveSuccess;
    }

    private String getToken() {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", MediaType.APPLICATION_FORM_URLENCODED_VALUE);
            headers.set("X-lgsp-requesttime", PortalUtil.getCurrentTimeForLGSP());
            headers.set("X-lgsp-authorization",
                    "Basic ".concat(Base64.getEncoder().encodeToString((basicUsername + ":" + basicPassword).getBytes())));

            String jsonBody = String.format(
                    "username=%s&password=%s&grant_type=%s",
                    username,
                    password,
                    "password"
            );
            log.info("body: " + jsonBody);
            log.info("header auth: " + headers.get("X-lgsp-authorization"));
            HttpEntity<String> entity = new HttpEntity<>(jsonBody, headers);
            log.info("Calling API LGSP: " + hostLgsp + tokenUrl);
            ResponseEntity<String> response = restTemplate.postForEntity(hostLgsp + tokenUrl, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Request getToken was successful: " + response.getBody());

                JSONObject responseJSON = new JSONObject(response.getBody());
                return responseJSON.getString(ApiConstant.ACCESS_TOKEN);
            }

            log.warn("Request getToken failed with status: " + response.getStatusCode());

        } catch (Exception e) {
            log.error("Error when getting token: ", e);
        }
        return null;
    }

    private boolean sendEdocUseSDK(SDKVXPAction action, HangDoiGoiTin hangDoiGoiTin) {
        String urlLocalFile    = hangDoiGoiTin.getNoiDungGoiTin().getDuongDanURL();
        String from = hangDoiGoiTin.getNoiGuiGoiTin().getMaKetNoi();
        Map<String, String> headers = hangDoiGoiTin.getHeaders();
        String serviceType = "eDoc";
        String messageType = headers.get(Constant.HEADER_MESSAGE_TYPE);

        SendEdocResult result = action.sendEdoc(from, serviceType, messageType, urlLocalFile);
        if(Validator.isNull(result)) {
            log.warn("Request sendEdoc is null");
            return false;
        }

        if(result.getStatus().equalsIgnoreCase(Constant.MESSAGE_OKE)) {
            log.warn("Request sendEdoc is oke for unit " + from + ", docId: " + result.getDocID());
            return true;
        }

        log.warn("Request sendEdoc is error with status: " + result.getStatus() + ", errorDesc: " + result.getErrorDesc()
                + ", errorCode: " + result.getErrorCode() + ", docId: " + result.getDocID());
        return false;
    }

    private String getEdocUseSDK(SDKVXPAction action, String edocId) {
        GetEdocResult getEdocResult = action.getEdoc(edocId);
        if(Validator.isNull(getEdocResult) || getEdocResult.getFilePath().isEmpty()) {
            log.warn("File not found when call SDK for edocId: " + edocId);
            return null;
        }
        return getEdocResult.getFilePath();
    }

    private JSONArray getListEdocUseSDK(SDKVXPAction action) {
        JSONArray result = new JSONArray();
        GetReceivedEdocResult edocsResult = action.getListEdoc("eDoc", Constant.MESSAGE_TYPE_EDOC);

        if(Validator.isNull(edocsResult) || edocsResult.getKnobsticks().size() == 0) {
            return result;
        }
        JSONObject oneResult;
        for(Knobstick item : edocsResult.getKnobsticks()){
            oneResult = new JSONObject();
            oneResult.put("docId", item.getId());
            oneResult.put("serviceType", item.getServiceType());
            oneResult.put("created_time", item.getCreatedTime());
            oneResult.put("updated_time", item.getUpdatedTime());
            oneResult.put("messagetype", item.getMessageType());
            oneResult.put("status", item.getStatus());
            oneResult.put("from", item.getFrom());
            oneResult.put("to", item.getTo());

            result.put(oneResult);
        }
        return result;
    }

    private boolean updateStatusUseSDK(SDKVXPAction action, String edocId, String status) {
        GetChangeStatusResult getChangeStatusResult= action.updateStatus(status, edocId);

        if(Validator.isNull(getChangeStatusResult)) {
            log.warn("getChangeStatusResult is null for edocId: " + edocId + ", status: " + status);
            return false;
        }

        if(getChangeStatusResult.getStatus().equalsIgnoreCase(Constant.MESSAGE_OKE)) {
            return true;
        }

        return false;
    }




    private boolean sendEdoc(String token, HangDoiGoiTin hangDoiGoiTin, String maHeThongXuly) {

        try {
            log.info("token sendEdoc: " + token);
            String duongDanTep    = hangDoiGoiTin.getNoiDungGoiTin().getDuongDanURL();
            HttpHeaders headers = new HttpHeaders();
            headers.set("SystemId", systemId);
            headers.set("Content-Type", MediaType.APPLICATION_XML_VALUE);
            headers.set("MessageType", "edoc");
            headers.set("ServiceType", "eDoc");
            headers.set("x-lgsp-requesttime", PortalUtil.getCurrentTimeForLGSP());
            headers.set("X-lgsp-serviceId", serviceId);
            headers.set("x-lgsp-authorization", ApiConstant.BEARER + " " + token);
            headers.set("X-lgsp-requestId", UUID.randomUUID().toString());
            headers.set("x-lgsp-version", version);

            hangDoiGoiTin.getHeaders().forEach((key, value) -> {
                headers.set(key, value);
            });
            log.info("Duong dan tep: " + duongDanTep);
            File file = new File(duongDanTep);

            if(Validator.isNull(file)) {
                log.warn("File not found with url: " + duongDanTep);
                return false;
            }

            String fileContent = new String(Files.readAllBytes(file.toPath()));
            log.info("FileContent: " + fileContent);
            HttpEntity<String> entity = new HttpEntity<>(fileContent, headers);
            log.info("Calling API LGSP: " + hostLgsp + sendEdocUrl);
            ResponseEntity<String> response = restTemplate.postForEntity(hostLgsp + sendEdocUrl, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Request sendEdoc was successful: " + response.getBody());

                JSONObject responseJSON = new JSONObject(response.getBody());

                if(!responseJSON.has("code") || !responseJSON.getString("code").equalsIgnoreCase(Constant.CODE_OKE)) {
                    log.warn("Request sendEdoc error with message: "
                            + responseJSON.getString("message") + ", code: " + responseJSON.getString("code"));
                    return false;
                }

                if(responseJSON.has("data") && Validator.isNotNull(responseJSON.getJSONArray("data"))) {
                    JSONObject oneData = responseJSON.getJSONArray("data").getJSONObject(0);
                    if(oneData.has("docId") && Validator.isNotNull(oneData.getString("docId"))) {
                        return true;
                    }
                }

                log.warn("Request sendEdoc has no docId or docId is empty");
                return false;
            }
            log.warn("Request sendEdoc failed with http status: " + response.getStatusCode());

        }  catch (Exception e) {
            log.error("Error when sendEdoc: ", e);
        }
        return false;
    }

    private JSONArray getGetReceivedEdocList(String token, String clientReceiveEdoc) {
        JSONArray result = new JSONArray();
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("SystemId", systemId);
            headers.set("Content-Type", MediaType.APPLICATION_JSON_VALUE);
            headers.set("MessageType", "edoc");
            headers.set("X-lgsp-from", "notImportant");
            // X-lgsp-to là don vi nhan goi tin
            headers.set("X-lgsp-to", clientReceiveEdoc);
            headers.set("x-lgsp-requesttime", PortalUtil.getCurrentTimeForLGSP());
            headers.set("X-lgsp-serviceId", serviceId);
            headers.set("x-lgsp-authorization", ApiConstant.BEARER + " " + token);
            headers.set("X-lgsp-requestId", UUID.randomUUID().toString());
            headers.set("x-lgsp-version", version);

            HttpEntity<String> entity = new HttpEntity<>(headers);
            log.info("Calling API LGSP: " + hostLgsp + getReceivedEdocListUrl);
            ResponseEntity<String> response = restTemplate.exchange(hostLgsp + getReceivedEdocListUrl, HttpMethod.POST, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Request getGetReceivedEdocList was successful: " + response.getBody());

                JSONObject responseJSON = new JSONObject(response.getBody());

                if(!responseJSON.has("code") || !responseJSON.getString("code").equalsIgnoreCase(Constant.CODE_OKE)) {
                    log.warn("Request getGetReceivedEdocList error with message: "
                            + responseJSON.getString("message") + ", code: " + responseJSON.getString("code"));
                    return result;
                }

                if(responseJSON.has("data") && Validator.isNotNull(responseJSON.getJSONObject("data"))
                        && responseJSON.getJSONObject("data").has("listResponse")) {
                    JSONArray data = responseJSON.getJSONObject("data").getJSONArray("listResponse");
                    if(data.length() > 0) {
                        return data;
                    }
                }

                log.warn("Request getGetReceivedEdocList has no DATA or DATA is empty");
                return result;
            }
            log.warn("Request getGetReceivedEdocList failed with http status: " + response.getStatusCode());
        }  catch (Exception e) {
            log.error("Error when getGetReceivedEdocList: ", e);
        }
        return result;
    }

    private String getEdoc(String edocId, String clientReceiveEdoc, String token) {
        String result = "";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("SystemId", systemId);
            headers.set("Content-Type", MediaType.APPLICATION_JSON_VALUE);
            headers.set("DocId", edocId);
            headers.set("X-lgsp-from", "notImportant");
            headers.set("X-lgsp-to", "notImportant");
            headers.set("x-lgsp-requesttime", PortalUtil.getCurrentTimeForLGSP());
            headers.set("X-lgsp-serviceId", serviceId);
            headers.set("x-lgsp-authorization", ApiConstant.BEARER + " " + token);
            headers.set("X-lgsp-requestId", UUID.randomUUID().toString());
            headers.set("x-lgsp-version", version);

            HttpEntity<String> entity = new HttpEntity<>(headers);
            log.info("Calling API LGSP: " + hostLgsp + getEdocUrl);
            ResponseEntity<String> response = restTemplate.exchange(hostLgsp + getEdocUrl, HttpMethod.POST, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Request getEdoc" + edocId +" was successful ");

                JSONObject responseJSON = new JSONObject(response.getBody());

                if(!responseJSON.has("code") || !responseJSON.getString("code").equalsIgnoreCase(Constant.CODE_OKE)) {
                    log.warn("Request getEdoc" + edocId +" error with message: "
                            + responseJSON.getString("message") + ", code: " + responseJSON.getString("code"));
                    return result;
                }

                if(responseJSON.has("data")) {
                    return responseJSON.getString("data");
                }

                log.warn("Request getGetReceivedEdocList has no DATA or DATA is empty");
                return result;

            }
            log.warn("Request getEdoc" + edocId +" failed with http status: " + response.getStatusCode());

        }  catch (Exception e) {
            log.error("Error when getEdoc " + edocId + ": ", e);
        }
        return result;
    }

    private boolean updateStatus(String edocId, String status, String clientReceiveEdoc, String token) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("SystemId", systemId);
            headers.set("Content-Type", MediaType.APPLICATION_JSON_VALUE);
            headers.set("DocId", edocId);
            headers.set("Status", status);
            headers.set("X-lgsp-from", clientReceiveEdoc);
            headers.set("X-lgsp-to", lgspTo);
            headers.set("x-lgsp-requesttime", PortalUtil.getCurrentTimeForLGSP());
            headers.set("X-lgsp-serviceId", serviceId);
            headers.set("x-lgsp-authorization", ApiConstant.BEARER + " " + token);
            headers.set("X-lgsp-requestId", UUID.randomUUID().toString());
            headers.set("x-lgsp-version", version);

            HttpEntity<String> entity = new HttpEntity<>(headers);
            log.info("Calling API LGSP: " + hostLgsp + updateStatusUrl);
            ResponseEntity<String> response = restTemplate.exchange(hostLgsp + updateStatusUrl, HttpMethod.POST, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Request updateStatus" + edocId +" was successful: " + response.getBody());

                JSONObject responseJSON = new JSONObject(response.getBody());

                if(!responseJSON.has("code") || !responseJSON.getString("code").equalsIgnoreCase(Constant.CODE_OKE)) {
                    log.warn("Request updateStatus" + edocId +" error with message: "
                            + responseJSON.getString("message") + ", code: " + responseJSON.getString("code"));
                    return false;
                }

                if(responseJSON.has("code") && responseJSON.getString("code").equalsIgnoreCase(Constant.CODE_OKE)) {
                    return true;
                }

                log.warn("Request updateStatus has no code or code is empty");
                return false;
            }
            log.warn("Request code" + edocId +" failed with http status: " + response.getStatusCode());
        } catch (Exception e) {
            log.error("Error when updateStatus " + edocId + ": ", e);
        }
        return false;
    }

    private HangDoiGoiTin saveEdocVer2(JSONObject oneEdocJson, TrucTichHop trucTichHop) {
        try {
            String edocId = oneEdocJson.get("docId").toString();
            String timeVdxpCreate = oneEdocJson.get("created_time").toString();
            String timeVdxpUpdate = oneEdocJson.get("updated_time").toString();
            String serviceType = oneEdocJson.get("serviceType").toString();
            String messagetype = oneEdocJson.get("messagetype").toString();
            String status = oneEdocJson.get("status").toString();
            String maNoiGui = oneEdocJson.get("from").toString();
            String maNoiNhan = oneEdocJson.get("to").toString();
            String urlFileSaved = oneEdocJson.get("urlFile").toString();
            String maGoiTin   = edocId;
            String maDinhDanh = UUID.randomUUID().toString();

            HangDoiGoiTin hangDoiGoiTin = new HangDoiGoiTin(false);
            List<HeThongKetNoiExt> heThongKetNoiExts = trucTichHop.getHeThongKetNoi();
            TrangThaiLienThongExt trangThaiLienThongExt = new TrangThaiLienThongExt();
            for(HeThongKetNoiExt heThongKetNoiExt: heThongKetNoiExts) {
                if(maNoiGui.equals(heThongKetNoiExt.getMaKetNoi())) {
                    heThongKetNoiExt.setThoiGianGui(System.currentTimeMillis());
                    heThongKetNoiExt.setTimeVdxpCreate(timeVdxpCreate);
                    heThongKetNoiExt.setTimeVdxpUpdate(timeVdxpUpdate);
                    hangDoiGoiTin.setNoiGuiGoiTin(heThongKetNoiExt);
                }

                if(maNoiNhan.equals(heThongKetNoiExt.getMaKetNoi())) {
                    heThongKetNoiExt.setTrangThaiLienThong(trangThaiLienThongExt);
                    heThongKetNoiExt.setTimeVdxpCreate(timeVdxpCreate);
                    heThongKetNoiExt.setTimeVdxpUpdate(timeVdxpUpdate);
                    hangDoiGoiTin.getNoiNhanGoiTin().add(heThongKetNoiExt);
                }
            }

            if(hangDoiGoiTin.getNoiNhanGoiTin().isEmpty()) {
                log.warn("Save hangdoigoitin has been halt because of maNoiNhan hasn't register on TrucVanBan: " + maNoiNhan);
                return null;
            }

            TepDuLieu tepDuLieu = tepDuLieuService.saveFileUrl(urlFileSaved);
            hangDoiGoiTin.getNoiDungGoiTin().setMaDinhDanh(tepDuLieu.getMaDinhDanh());
            hangDoiGoiTin.setMaDinhDanh(maDinhDanh);
            hangDoiGoiTin.setMaGoiTin(maGoiTin);
            hangDoiGoiTin.getTrucTichHop().setMaMuc(trucTichHop.getMaMuc());
            hangDoiGoiTin.setKieuLoaiGoiTin(Constant.GET_EDOC);
            hangDoiGoiTin.setDinhDangGoiTin(messagetype);
            return hangDoiGoiTinService.update(hangDoiGoiTin);

        }catch (Exception e) {
            log.error("Error when saveEdocToHangDoi " + oneEdocJson.get("docId") + ": ", e);
        }
        return null;
    }

    private HangDoiGoiTin saveEdocToHangDoi(String edocId, String edocResult, String kieuLoaiGoiTin,
                                      String maNoiGui, String maNoiNhan, TrucTichHop trucTichHop) {
        try {
            HangDoiGoiTin hangDoiGoiTin = new HangDoiGoiTin(false);
            String maDinhDanh = UUID.randomUUID().toString();
            String maGoiTin   = edocId;
            List<HeThongKetNoiExt> heThongKetNoiExts = trucTichHop.getHeThongKetNoi();
            TrangThaiLienThongExt trangThaiLienThongExt = new TrangThaiLienThongExt();
            for(HeThongKetNoiExt heThongKetNoiExt: heThongKetNoiExts) {
                if(maNoiGui.equals(heThongKetNoiExt.getMaKetNoi())) {
                    heThongKetNoiExt.setThoiGianGui(System.currentTimeMillis());
                    hangDoiGoiTin.setNoiGuiGoiTin(heThongKetNoiExt);
                }

                if(maNoiNhan.equals(heThongKetNoiExt.getMaKetNoi())) {
                    heThongKetNoiExt.setTrangThaiLienThong(trangThaiLienThongExt);
                    hangDoiGoiTin.getNoiNhanGoiTin().add(heThongKetNoiExt);
                }
            }

            TepDuLieu tepDuLieuGoc = tepDuLieuService.saveFileText(edocResult, true);

            String edocDecoded = new String(Base64.getDecoder().decode(edocResult));

            TepDuLieu tepDuLieuDecoded = tepDuLieuService.saveFileText(edocDecoded, false);

            if(Validator.isNull(tepDuLieuDecoded) || Validator.isNull(tepDuLieuGoc)){
                log.warn("Tep du lieu is null when trying to saveEdocToHangDoi");
                return null;
            }

            hangDoiGoiTin.getNoiDungGoiTin().setMaDinhDanh(tepDuLieuDecoded.getMaDinhDanh());
            hangDoiGoiTin.getNoiDungGoiTin().setFileOriginal(tepDuLieuGoc.getMaDinhDanh());
            hangDoiGoiTin.setMaDinhDanh(maDinhDanh);
            hangDoiGoiTin.setMaGoiTin(maGoiTin);
            hangDoiGoiTin.getTrucTichHop().setMaMuc(trucTichHop.getMaMuc());
            hangDoiGoiTin.setKieuLoaiGoiTin(kieuLoaiGoiTin);
            return hangDoiGoiTinService.update(hangDoiGoiTin);
        } catch (Exception e) {
            log.error("Error when saveEdocToHangDoi " + 0 + ": ", e);
        }
        return null;
    }

}
